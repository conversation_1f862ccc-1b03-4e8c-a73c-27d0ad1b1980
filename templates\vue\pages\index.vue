<template>
  <div id="app">
    <!-- 全屏背景图片 -->
    <img src="/static/images/bj.png" alt="Full Background" class="full-bg full-bg-bottom animated pulse" ondragstart="return false;" oncontextmenu="return false;">

    <br>
    <div class="col-xs-12 col-sm-10 col-md-8 col-lg-5 center-block" style="float: none;">

      <!-- 弹出公告模态框 -->
      <div class="modal fade" align="left" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showModal">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header-tabs">
              <button type="button" class="close" @click="showModal = false">
                <span aria-hidden="true">×</span>
                <span class="sr-only">Close</span>
              </button>
              <h4 class="modal-title" id="myModalLabel">彩虹云商城</h4>
            </div>
            <div class="modal-body">
              <!-- 公告内容 -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-default" @click="showModal = false">知道啦</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台公告模态框 -->
      <div class="modal fade" align="left" id="anounce" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showAnnounceModal" style="display: none;">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header" style="background:linear-gradient(120deg, #31B404 0%, #D7DF01 100%);">
              <button type="button" class="close" @click="showAnnounceModal = false">
                <span aria-hidden="true"></span>
                <span class="sr-only">Close</span>
              </button>
              <center>
                <h4 class="modal-title" id="myModalLabel">
                  <b><font color="#fff">彩虹云商城</font></b>
                </h4>
              </center>
            </div>
            <div class="widget flat radius-bordered">
              <div class="widget-header bordered-top bordered-themesecondary">
                <div class="modal-body">
                  <p>
                    <li class="list-group-item">
                      <span class="btn btn-danger btn-xs">1</span> 售后问题可直接联系平台在线QQ客服
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-success btn-xs">2</span> 下单之前请一定要看完该商品的注意事项再进行下单！
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-info btn-xs">3</span> 所有业务全部恢复，都可以正常下单，欢迎尝试
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-warning btn-xs">4</span> 温馨提示：请勿重复下单哦！必须要等待前面任务订单完成才可以下单！
                    </li>
                    <li class="list-group-item">
                      <span class="btn btn-primary btn-xs">5</span>
                      <a href="./user/regsite.php">价格贵？不怕，点击0元搭建，在后台超低价下单！</a>
                    </li>
                    <div class="btn-group btn-group-justified">
                      <a target="_blank" class="btn btn-info" href="http://wpa.qq.com/msgrd?v=3&uin=123456&site=qq&menu=yes">
                        <i class="fa fa-qq"></i> 联系客服
                      </a>
                      <a target="_blank" class="btn btn-warning" href="http://qun.qq.com/join.html">
                        <i class="fa fa-users"></i> 官方Q群
                      </a>
                      <a target="_blank" class="btn btn-danger" href="./">
                        <i class="fa fa-cloud-download"></i> APP下载
                      </a>
                    </div>
                  </p>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-default" @click="showAnnounceModal = false">我明白了</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Logo区域 -->
      <div class="widget">
        <div class="widget-content themed-background-flat text-center" style="background-image:url(/static/images/baiyun.jpg);background-size: 100% 100%;">
          <a href="javascript:void(0)">
            <img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&spec=100" alt="Avatar" width="80" style="height: auto filter: alpha(Opacity=80);-moz-opacity: 0.80;opacity: 0.80;" class="img-circle img-thumbnail img-thumbnail-avatar-1x animated zoomInDown">
          </a>
        </div>

        <center>
          <h2>
            <a href="javascript:void(alert('彩虹云商城，建议收藏到浏览器书签哦！'));">
              <b>彩虹云商城</b>
            </a>
          </h2>
        </center>

        <!-- Logo下面按钮 -->
        <div class="widget-content text-center">
          <div class="text-center text-muted">
            <div class="btn-group btn-group-justified">
              <div class="btn-group">
                <a class="btn btn-default" @click="showAnnounceModal = true">
                  <i class="fa fa-bullhorn"></i>&nbsp;<span style="font-weight:bold">公告</span>
                </a>
              </div>
              <a href="#lxkf" target="_blank" @click="showCustomerServiceModal = true" class="btn btn-default">
                <i class="fa fa-qq"></i>&nbsp;<span style="font-weight:bold">客服</span>
              </a>
              <div class="btn-group">
                <a class="btn btn-default" href="user/login.php">
                  <i class="fa fa-users fa-1x"></i>&nbsp;登录
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab导航区域 -->
      <div class="block full2">
        <div class="block-title">
          <ul class="nav nav-tabs" data-toggle="tabs">
            <li style="width: 25%;" align="center" :class="{ active: activeTab === 'shop' }">
              <a href="#shop" @click="activeTab = 'shop'">
                <i class="fa fa-shopping-cart"></i> <b>下单</b>
              </a>
            </li>
            <li style="width: 25%;" align="center" :class="{ active: activeTab === 'search' }">
              <a href="#search" @click="activeTab = 'search'" id="tab-query">
                <i class="fa fa-search"></i> <b>查询</b>
              </a>
            </li>
            <li style="width: 25%;" align="center" :class="{ active: activeTab === 'substation' }">
              <a href="#Substation" @click="activeTab = 'substation'">
                <font color="#FF4000">
                  <i class="fa fa-location-arrow fa-spin"></i> <b>分站</b>
                </font>
              </a>
            </li>
            <li style="width: 25%;" align="center" :class="{ active: activeTab === 'more' }">
              <a href="#more" @click="activeTab = 'more'">
                <i class="fa fa-list"></i> <b>更多</b>
              </a>
            </li>
          </ul>
        </div>

        <!-- Tab内容区域 -->
        <div class="tab-content">
          <!-- 在线下单 -->
          <div class="tab-pane" :class="{ active: activeTab === 'shop' }" id="shop">
            <center>
              <div class="shuaibi-tip animated tada text-center">
                <i class="fa fa-heart text-danger"></i>
                <b>［07月30号］最新业务通知&nbsp;
                  <a href="#anounce" @click="showAnnounceModal = true" class="label label-danger">
                    <font color="#FFFFFF">点击查看</font>
                  </a>
                </b>
              </div>
            </center>

            <div id="goodTypeContents">
              <div class="form-group" id="display_searchBar">
                <div class="input-group">
                  <div class="input-group-addon">搜索商品</div>
                  <input type="text" id="searchkw" class="form-control" placeholder="搜索商品" v-model="searchKeyword" @keydown.enter="doSearch"/>
                  <div class="input-group-addon">
                    <span class="glyphicon glyphicon-search onclick" title="搜索" id="doSearch" @click="doSearch"></span>
                  </div>
                </div>
              </div>

              <div class="form-group" id="display_selectclass" style="display:none;">
                <div class="input-group">
                  <div class="input-group-addon">选择分类</div>
                  <select name="tid" id="cid" class="form-control" v-model="selectedCategory">
                    <option value="0">请选择分类</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <div class="input-group">
                  <div class="input-group-addon">选择商品</div>
                  <select name="tid" id="tid" class="form-control" v-model="selectedProduct" @change="getPoint">
                    <option value="0">请选择商品</option>
                  </select>
                </div>
              </div>

              <div class="form-group" id="display_price" style="display:none;text-align:center;color:#4169E1;font-weight:bold">
                <div class="input-group">
                  <div class="input-group-addon">商品价格</div>
                  <input type="text" name="need" id="need" class="form-control" style="text-align:center;color:#4169E1;font-weight:bold" disabled v-model="productPrice"/>
                </div>
              </div>

              <div class="form-group" id="display_left" style="display:none;">
                <div class="input-group">
                  <div class="input-group-addon">库存数量</div>
                  <input type="text" name="leftcount" id="leftcount" class="form-control" disabled v-model="stockCount"/>
                </div>
              </div>

              <div class="form-group" id="display_num" style="display:none;">
                <div class="input-group">
                  <div class="input-group-addon">下单份数</div>
                  <span class="input-group-btn">
                    <input id="num_min" type="button" class="btn btn-info" style="border-radius: 0px;" value="━" @click="decreaseQuantity">
                  </span>
                  <input id="num" name="num" class="form-control" type="number" min="1" v-model="orderQuantity"/>
                  <span class="input-group-btn">
                    <input id="num_add" type="button" class="btn btn-info" style="border-radius: 0px;" value="✚" @click="increaseQuantity">
                  </span>
                </div>
              </div>

              <div id="inputsname"></div>

              <div id="alert_frame" class="alert alert-success animated rubberBand" style="display:none;background: linear-gradient(to right,#71D7A2,#5ED1D7);font-weight: bold;color:white;" v-show="showAlert" v-html="alertMessage"></div>

              <div class="btn-group btn-group-justified form-group">
                <a class="btn btn-block btn-success" type="button" id="submit_cart_shop" @click="addToCart">加入购物车</a>
                <a type="submit" id="submit_buy" class="btn btn-block btn-primary" @click="buyNow">立即购买</a>
              </div>

              <div class="panel-body border-t" id="alert_cart" style="display:none;" v-show="cartCount > 0">
                <i class="fa fa-shopping-cart"></i>&nbsp;当前购物车已添加<b id="cart_count">{{ cartCount }}</b>个商品
                <a class="btn btn-xs btn-danger pull-right" href="javascript:openCart()" @click="openCart">购物车列表</a>
              </div>
            </div>
          </div>

          <!-- 查询订单 -->
          <div class="tab-pane" :class="{ active: activeTab === 'search' }" id="search">
            <table class="table table-striped table-borderless table-vcenter remove-margin-bottom">
              <tbody>
                <tr class="shuaibi-tip animation-bigEntrance">
                  <td class="text-center" style="width: 100px;">
                    <img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&spec=100" alt="avatar" class="img-circle img-thumbnail img-thumbnail-avatar">
                  </td>
                  <td>
                    <h4><strong>站长</strong></h4>
                    <i class="fa fa-fw fa-qq text-primary"></i> 123456789<br>
                    <i class="fa fa-fw fa-history text-danger"></i>售后订单问题请联系客服
                  </td>
                  <td class="text-right" style="width: 20%;">
                    <a href="#lxkf" target="_blank" @click="showCustomerServiceModal = true" class="btn btn-sm btn-info">联系</a>
                  </td>
                </tr>
              </tbody>
            </table>

            <br>
            <div class="col-xs-12 well well-sm animation-pullUp">
              <span class="label label-primary">待处理</span> 说明正在努力提交到服务器！<p></p><p></p>
              <span class="label label-success">已完成</span> 已经提交到接口正在处理！<p></p><p></p>
              <span class="label label-warning">处理中</span> 已经开始为您开单 请耐心等！<p></p><p></p>
              <span class="label label-danger">有异常</span> 下单信息有误 联系客服处理！
            </div>

            <div class="form-group">
              <div class="input-group">
                <div class="input-group-btn">
                  <select class="form-control" id="searchtype" style="padding: 6px 4px;width:90px" v-model="searchType">
                    <option value="0">下单账号</option>
                    <option value="1">订单号</option>
                  </select>
                </div>
                <input type="text" name="qq" id="qq3" v-model="searchQuery" class="form-control" placeholder="请输入要查询的内容（留空则显示最新订单）" @keydown.enter="submitQuery" required="">
                <span class="input-group-btn">
                  <a tabindex="0" class="btn btn-default" role="button" data-container="body" data-toggle="popover" data-trigger="focus" data-placement="top" title="查询内容是什么？" data-content="请输入您下单时，在第一个输入框内填写的信息。如果您不知道下单账号是什么，可以不填写，直接点击查询，则会根据浏览器缓存查询！">
                    <i class="glyphicon glyphicon-exclamation-sign"></i>
                  </a>
                </span>
              </div>
            </div>

            <input type="submit" id="submit_query" class="btn btn-primary btn-block btn-rounded" style="background: linear-gradient(to right,#87CEFA,#6495ED);color:#fff;" value="立即查询" @click="submitQuery">

            <br>
            <div id="result2" class="form-group" style="display:none;" v-show="showQueryResult">
              <center><small><font color="#ff0000">手机用户可以左右滑动</font></small></center>
              <div class="table-responsive">
                <table class="table table-vcenter table-condensed table-striped">
                  <thead>
                    <tr>
                      <th>下单账号</th>
                      <th>商品名称</th>
                      <th>数量</th>
                      <th class="hidden-xs">购买时间</th>
                      <th>状态</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody id="list">
                    <tr v-for="order in queryResults" :key="order.id">
                      <td>{{ order.account }}</td>
                      <td>{{ order.productName }}</td>
                      <td>{{ order.quantity }}</td>
                      <td class="hidden-xs">{{ order.createTime }}</td>
                      <td>{{ order.status }}</td>
                      <td>{{ order.action }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 开通分站 -->
          <div class="tab-pane animation-fadeInQuick2" :class="{ active: activeTab === 'substation' }" id="Substation">
            <table class="table table-borderless table-pricing">
              <tbody>
                <tr class="active">
                  <td class="btn-effect-ripple" style="overflow: hidden; position: relative;width: 100%; height: 8em;display: block;color: white;margin: auto;background-color: lightskyblue;">
                    <span class="btn-ripple animate" style="height: 546px; width: 546px; top: -212.8px; left: 56.4px;"></span>
                    <h3 style="width:100%;font-size: 1.6em;">
                    </h3>
                    <h3 style="width:100%;font-size: 1.6em;">
                      <i class="fa fa-user-o fa-fw" style="margin-top: 0.7em;"></i><strong>入门级</strong> /
                      <i class="fa fa-user-circle-o fa-fw"></i><strong>旗舰级</strong>
                    </h3>
                    <span style="width: 100%;text-align: center;margin-top: 0.8em;font-size: 1.1em;display: block;">10元 / 20元</span>
                  </td>
                </tr>
                <tr>
                  <td>一模一样的独立网站</td>
                </tr>
                <tr>
                  <td>站长后台和超低秘价</td>
                </tr>
                <tr>
                  <td>余额提成满10元提现</td>
                </tr>
                <tr>
                  <td><strong>旗舰级可以吃下级分站提成</strong></td>
                </tr>
                <tr class="active">
                  <td>
                    <a href="#userjs" @click="showVersionModal = true" class="btn btn-effect-ripple btn-info" style="overflow: hidden; position: relative;">
                      <i class="fa fa-align-justify"></i>
                      <span class="btn-ripple animate" style="height: 100px; width: 100px; top: -24.8px; left: 11.05px;"></span> 版本介绍
                    </a>
                    <a href="user/regsite.php" target="_blank" class="btn btn-effect-ripple btn-danger" style="overflow: hidden; position: relative;">
                      <i class="fa fa-arrow-right"></i> 马上开通
                    </a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 更多按钮 -->
          <div class="tab-pane" :class="{ active: activeTab === 'more' }" id="more">
            <div class="row">
              <div class="col-sm-6">
                <a href="./user/" target="_blank" class="widget">
                  <div class="widget-content themed-background-info text-right clearfix" style="color: #fff;">
                    <div class="widget-icon pull-left">
                      <i class="fa fa-certificate"></i>
                    </div>
                    <h2 class="widget-heading h3">
                      <strong>分站后台</strong>
                    </h2>
                    <span>登录分站后台</span>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="panel panel-primary">
        <div class="panel-heading">
          <h3 class="panel-title">
            <font color="#000000">
              <i class="fa fa-bar-chart-o"></i>&nbsp;&nbsp;<b>数据统计</b>
            </font>
          </h3>
        </div>
        <table class="table table-bordered">
          <tbody>
            <tr>
              <td align="center">
                <font size="2">
                  <span id="count_yxts">{{ statistics.operatingDays }}</span>天<br>
                  <font color="#65b1c9"><i class="fa fa-shield fa-2x"></i></font><br>
                  安全运营
                </font>
              </td>
              <td align="center">
                <font size="2">
                  <span id="count_money">{{ statistics.totalAmount }}</span>元<br>
                  <font color="#65b1c9"><i class="fa fa-shopping-cart fa-2x"></i></font><br>
                  交易总数
                </font>
              </td>
              <td align="center">
                <font size="2">
                  <span id="count_orders">{{ statistics.totalOrders }}</span>笔<br>
                  <font color="#65b1c9"><i class="fa fa-check-square-o fa-2x"></i></font><br>
                  订单总数
                </font>
              </td>
            </tr>
            <tr>
              <td align="center">
                <font size="2">
                  <span id="count_site">{{ statistics.agentSites }}</span>个<br>
                  <font color="#65b1c9"><i class="fa fa-sitemap fa-2x"></i></font><br>
                  代理分站
                </font>
              </td>
              <td align="center">
                <font size="2">
                  <span id="count_money1">{{ statistics.todayAmount }}</span>元<br>
                  <font color="#65b1c9"><i class="fa fa-pie-chart fa-2x"></i></font><br>
                  今日交易
                </font>
              </td>
              <td align="center">
                <font size="2">
                  <span id="count_orders2">{{ statistics.todayOrders }}</span>笔<br>
                  <font color="#65b1c9"><i class="fa fa-check-square fa-2x"></i></font><br>
                  今日订单
                </font>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 底部导航 -->
      <div class="panel panel-default">
        <center>
          <div class="panel-body">
            <span style="font-weight:bold">彩虹云商城 <i class="fa fa-heart text-danger"></i> 2025 | </span>
            <a href="./"><span style="font-weight:bold">118.89.145.190</span></a><br/>
          </div>
        </center>
      </div>
    </div>

    <!-- 客服介绍模态框 -->
    <div class="modal fade col-xs-12" align="left" id="lxkf" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showCustomerServiceModal">
      <br><br>
      <div class="modal-dialog panel panel-primary animation-fadeInQuick2">
        <div class="modal-content">
          <div class="list-group-item reed" style="background:linear-gradient(120deg, #5ED1D7 10%, #71D7A2 90%);">
            <button type="button" class="close" @click="showCustomerServiceModal = false">
              <span aria-hidden="true"></span>
              <span class="sr-only">Close</span>
            </button>
            <center>
              <h4 class="modal-title" id="myModalLabel">
                <b><font color="#fff">客服与帮助</font></b>
              </h4>
            </center>
          </div>
          <div class="modal-body" id="accordion">
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">为什么订单显示已完成了却一直没到账？</a>
                </h4>
              </div>
              <div id="collapseOne" class="panel-collapse in" style="height: auto;">
                <div class="panel-body">
                  订单显示（已完成）就证明已经提交到服务器内！<br>
                  如果长时间没到账请联系客服处理！<br>
                  订单长时间显示（待处理）请联系客服！
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseTwo" class="collapsed">商品什么时候到账？</a>
                </h4>
              </div>
              <div id="collapseTwo" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body">
                  请参考商品简介里面，有关于到账时间的说明。
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseThree" class="collapsed">卡密没有发送我的邮箱？</a>
                </h4>
              </div>
              <div id="collapseThree" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body">
                  没有收到请检查自己邮箱的垃圾箱！也可以去查单区：输入自己下单时填写的邮箱进行查单。<br>
                  查询到订单后点击（详细）就可以看到自己购买的卡密！
                </div>
              </div>
            </div>
            <div class="panel panel-default" style="margin-bottom: 6px;">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#accordion" href="#collapseFourth" class="collapsed">已付款了没有查询到我订单？</a>
                </h4>
              </div>
              <div id="collapseFourth" class="panel-collapse collapse" style="height: 0px;">
                <div class="panel-body" style="margin-bottom: 6px;">
                  联系客服处理，请提供（付款详细记录截图）（下单商品名称）（下单账号）<br>
                  直接把三个信息发给客服，然后等待客服回复处理（请不要发抖动窗口或者QQ电话）！
                </div>
              </div>
            </div>
            <ul class="list-group" style="margin-bottom: 0px;">
              <li class="list-group-item">
                <div class="media">
                  <span class="pull-left thumb-sm">
                    <img src="//q4.qlogo.cn/headimg_dl?dst_uin=123456789&spec=100" alt="..." class="img-circle img-thumbnail img-avatar">
                  </span>
                  <div class="pull-right push-15-t">
                    <a href="http://wpa.qq.com/msgrd?v=3&uin=123456789&site=qq&menu=yes" target="_blank" class="btn btn-sm btn-info">联系</a>
                  </div>
                  <div class="pull-left push-10-t">
                    <div class="font-w600 push-5">订单售后客服</div>
                    <div class="text-muted"><b>QQ：123456789</b></div>
                  </div>
                </div>
              </li>
              <li class="list-group-item">
                想要快速回答你的问题就请把问题描述讲清楚!<br>
                下单账号+业务名称+问题，直奔主题，按顺序回复!<br>
                有问题直接留言，请勿抖动语音否则直接无视。<br>
              </li>
            </ul>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" @click="showCustomerServiceModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分站介绍模态框 -->
    <div class="modal fade" align="left" id="userjs" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" v-show="showVersionModal" style="display: none;">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="list-group-item reed" style="background:linear-gradient(120deg, #FE2EF7 10%, #71D7A2 90%);">
            <button type="button" class="close" @click="showVersionModal = false">
              <span aria-hidden="true"></span>
              <span class="sr-only">Close</span>
            </button>
            <center>
              <h4 class="modal-title" id="myModalLabel">
                <b><font color="#fff">版本介绍</font></b>
              </h4>
            </center>
          </div>
          <div class="modal-body">
            <div class="table-responsive">
              <table class="table table-borderless table-vcenter">
                <thead>
                  <tr>
                    <th style="width: 100px;">功能</th>
                    <th class="text-center" style="width: 20px;">普及版/专业版</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="active">
                    <td>独立网站/专属后台</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="">
                    <td>低价拿货/调整价格</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="info">
                    <td>搭建分站/管理分站</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="">
                    <td>超低密价/高额提成</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                  <tr class="danger">
                    <td>赠送专属APP</td>
                    <td class="text-center">
                      <span class="btn btn-effect-ripple btn-xs btn-danger" style="overflow: hidden; position: relative;">
                        <i class="fa fa-close"></i>
                      </span>
                      <span class="btn btn-effect-ripple btn-xs btn-success" style="overflow: hidden; position: relative;">
                        <i class="fa fa-check"></i>
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" @click="showVersionModal = false">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RainbowShop',
  data() {
    return {
      // 模态框控制
      showModal: false,
      showAnnounceModal: false,
      showCustomerServiceModal: false,
      showVersionModal: false,

      // Tab控制
      activeTab: 'shop',

      // 下单相关
      searchKeyword: '',
      selectedCategory: '0',
      selectedProduct: '0',
      productPrice: '',
      stockCount: '',
      orderQuantity: 1,
      cartCount: 0,
      showAlert: false,
      alertMessage: '',

      // 查询相关
      searchType: '0',
      searchQuery: '',
      showQueryResult: false,
      queryResults: [],

      // 统计数据
      statistics: {
        operatingDays: '9',
        totalAmount: '0',
        totalOrders: '0',
        agentSites: '0',
        todayAmount: '0',
        todayOrders: '0'
      }
    }
  },
  methods: {
    // 搜索商品
    doSearch() {
      console.log('搜索商品:', this.searchKeyword);
      // 实现搜索逻辑
    },

    // 获取商品信息
    getPoint() {
      console.log('获取商品信息:', this.selectedProduct);
      // 实现获取商品价格和库存逻辑
    },

    // 增加数量
    increaseQuantity() {
      this.orderQuantity++;
    },

    // 减少数量
    decreaseQuantity() {
      if (this.orderQuantity > 1) {
        this.orderQuantity--;
      }
    },

    // 加入购物车
    addToCart() {
      this.cartCount++;
      this.showAlert = true;
      this.alertMessage = '商品已加入购物车！';
      setTimeout(() => {
        this.showAlert = false;
      }, 3000);
    },

    // 立即购买
    buyNow() {
      console.log('立即购买');
      // 实现购买逻辑
    },

    // 打开购物车
    openCart() {
      console.log('打开购物车');
      // 实现购物车逻辑
    },

    // 提交查询
    submitQuery() {
      console.log('查询订单:', this.searchQuery, this.searchType);
      this.showQueryResult = true;
      // 模拟查询结果
      this.queryResults = [
        {
          id: 1,
          account: '<EMAIL>',
          productName: '测试商品',
          quantity: 1,
          createTime: '2025-07-30 12:00:00',
          status: '已完成',
          action: '查看详情'
        }
      ];
    }
  },

  mounted() {
    // 组件挂载后的初始化逻辑
    console.log('彩虹云商城页面已加载');
  }
}
</script>

<style scoped>
/* 导入外部CSS框架 */
@import url('//cdn.staticfile.org/twitter-bootstrap/3.3.7/css/bootstrap.min.css');
@import url('//cdn.staticfile.org/font-awesome/4.7.0/css/font-awesome.min.css');

/* 全局样式 */
body {
  background: #ecedf0 url("/static/images/bj.png") fixed;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #454e59;
}

/* 自定义提示框样式 */
.shuaibi-tip {
  background: #fafafa repeating-linear-gradient(-45deg,#fff,#fff 1.125rem,transparent 1.125rem,transparent 2.25rem);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  margin: 20px 0px;
  padding: 15px;
  border-radius: 5px;
  font-size: 14px;
  color: #555555;
}

/* 全屏背景图片 */
.full-bg {
  min-height: 100%;
  min-width: 1280px;
  width: 100%;
  height: auto;
  position: fixed;
  top: 0;
  left: 0;
}

.full-bg.full-bg-bottom {
  top: auto;
  bottom: 0;
}

@media screen and (max-width: 1280px) {
  .full-bg {
    left: 50%;
    margin-left: -640px;
  }
}

/* Widget组件样式 */
.widget {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
  border-radius: 2px;
}

.widget-content {
  padding: 15px;
}

/* Block组件样式 */
.block {
  margin: 0 0 10px;
  padding: 20px 15px 1px;
  background-color: #ffffff;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  box-shadow: 0 2px 0 rgba(218, 224, 232, .5);
}

.block.full2 {
  /* 保持原样式 */
}

.block-title {
  margin: -20px -15px 20px;
  border-bottom: 2px solid #dae0e8;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  background: rgba(218, 224, 232, .15);
}

.block-title .nav-tabs {
  padding: 0;
  margin: 0;
  border-bottom: none;
}

.block-title .nav-tabs > li > a {
  border: none;
  min-height: 40px;
  line-height: 40px;
  padding-top: 0;
  padding-bottom: 0;
  margin: 0;
  border-radius: 0;
}

.block-title .nav-tabs > li > a:hover {
  background-color: #ffffff;
}

.block-title .nav-tabs > li.active > a,
.block-title .nav-tabs > li.active > a:hover,
.block-title .nav-tabs > li.active > a:focus {
  border: none;
  background-color: #dae0e8;
}

/* Tab内容样式 */
.tab-content {
  padding: 20px 15px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* 表单样式 */
.form-control {
  padding: 6px 8px;
  max-width: 100%;
  margin: 1px 0;
  color: #454e59;
  border-color: #dae0e8;
  border-radius: 3px;
}

.form-control:focus {
  border-color: #5ccdde;
}

.input-group-addon {
  min-width: 45px;
  text-align: center;
  background-color: #ffffff;
  border-color: #dae0e8;
}

/* 按钮样式 */
.btn {
  border-radius: 3px;
}

.btn-primary {
  background-color: #5ccdde;
  border-color: #5ccdde;
}

.btn-primary:hover,
.btn-primary:focus {
  background-color: #4bb8c9;
  border-color: #4bb8c9;
}

.btn-success {
  background-color: #afde5c;
  border-color: #afde5c;
}

.btn-success:hover,
.btn-success:focus {
  background-color: #9bd147;
  border-color: #9bd147;
}

.btn-info {
  background-color: #5cafde;
  border-color: #5cafde;
}

.btn-warning {
  background-color: #deb25c;
  border-color: #deb25c;
}

.btn-danger {
  background-color: #de815c;
  border-color: #de815c;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  outline: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal[v-show="true"] {
  display: block;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}

.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, .2);
  border-radius: 6px;
  box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
  background-clip: padding-box;
  outline: 0;
}

.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}

.modal-body {
  position: relative;
  padding: 15px;
}

.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}

/* 表格样式 */
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd;
}

.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}

.table-bordered {
  border: 1px solid #ddd;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #ddd;
}

/* 面板样式 */
.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}

.panel-primary {
  border-color: #337ab7;
}

.panel-primary > .panel-heading {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}

.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.panel-body {
  padding: 15px;
}

/* 动画效果 */
.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.pulse {
  animation-name: pulse;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.9);
    opacity: 0.7;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.9);
    opacity: 0.7;
  }
}

.zoomInDown {
  animation-name: zoomInDown;
}

@keyframes zoomInDown {
  0% {
    opacity: 0;
    transform: scale3d(.1, .1, .1) translate3d(0, -1000px, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    transform: scale3d(.475, .475, .475) translate3d(0, 60px, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
  100% {
    transform: scale3d(1, 1, 1) translate3d(0, 0, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}

.tada {
  animation-name: tada;
}

@keyframes tada {
  0% {
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

.rubberBand {
  animation-name: rubberBand;
}

@keyframes rubberBand {
  0% {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    transform: scale3d(.95, 1.05, 1);
  }
  75% {
    transform: scale3d(1.05, .95, 1);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

.animation-bigEntrance {
  animation-name: bigEntrance;
  animation-duration: 1.6s;
  animation-timing-function: ease-out;
}

@keyframes bigEntrance {
  0% {
    transform: scale(0.3) rotate(6deg) translateX(-30%) translateY(30%);
    opacity: 0.2;
  }
  30% {
    transform: scale(1.03) rotate(-2deg) translateX(2%) translateY(-2%);
    opacity: 1;
  }
  45% {
    transform: scale(0.98) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  60% {
    transform: scale(1.01) rotate(-1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  75% {
    transform: scale(0.99) rotate(1deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  90% {
    transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
    opacity: 1;
  }
}

.animation-pullUp {
  animation-name: pullUp;
  animation-duration: 1.1s;
  animation-timing-function: ease-out;
  transform-origin: 50% 100%;
}

@keyframes pullUp {
  0% {
    transform: scaleY(0.1);
  }
  40% {
    transform: scaleY(1.02);
  }
  60% {
    transform: scaleY(0.98);
  }
  80% {
    transform: scaleY(1.01);
  }
  100% {
    transform: scaleY(1);
  }
}

.animation-fadeInQuick2 {
  animation-name: fadeInQuick2;
  animation-duration: .25s;
  animation-timing-function: ease-out;
}

@keyframes fadeInQuick2 {
  0% {
    transform: scale(.75);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
}

@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}

.clearfix:after {
  clear: both;
}

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* 颜色类 */
.text-primary {
  color: #5ccdde;
}

.text-success {
  color: #afde5c;
}

.text-info {
  color: #5cafde;
}

.text-warning {
  color: #deb25c;
}

.text-danger {
  color: #de815c;
}

.text-muted {
  color: #999999;
}

/* 边框工具类 */
.border-t {
  border-top: 1px solid #e9e9e9;
}

.border-b {
  border-bottom: 1px solid #e9e9e9;
}

/* 图片样式 */
.img-circle {
  border-radius: 50%;
}

.img-thumbnail {
  display: inline-block;
  max-width: 100%;
  height: auto;
  padding: 4px;
  line-height: 1.42857143;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  transition: all .2s ease-in-out;
}

/* 特殊效果 */
.onclick {
  cursor: pointer;
  touch-action: manipulation;
}

/* 隐藏类 */
.hidden-xs {
  display: block;
}

@media (max-width: 767px) {
  .hidden-xs {
    display: none;
  }
}

/* 按钮组 */
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}

.btn-group-justified > .btn,
.btn-group-justified > .btn-group {
  display: table-cell;
  float: none;
  width: 1%;
}

.btn-group-justified > .btn-group .btn {
  width: 100%;
}

/* 列表组 */
.list-group {
  padding-left: 0;
  margin-bottom: 20px;
}

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}

.list-group-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

/* 标签样式 */
.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
}

.label-primary {
  background-color: #337ab7;
}

.label-success {
  background-color: #5cb85c;
}

.label-info {
  background-color: #5bc0de;
}

.label-warning {
  background-color: #f0ad4e;
}

.label-danger {
  background-color: #d9534f;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  background-color: #777;
  border-radius: 10px;
}

/* 进度条 */
.progress {
  height: 20px;
  margin-bottom: 20px;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1);
}

.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background-color: #337ab7;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .15);
  transition: width .6s ease;
}

/* 警告框 */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.alert-success {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}

.alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}

.alert-danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}

/* 井样式 */
.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}

.well-sm {
  padding: 9px;
  border-radius: 3px;
}

/* 关闭按钮 */
.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=20);
  opacity: .2;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=50);
  opacity: .5;
}

/* 字体图标 */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

/* 特定组件样式 */
.themed-background-flat {
  background-color: #f9f9f9;
}

.widget-icon {
  display: inline-block;
  width: 64px;
  height: 64px;
  line-height: 60px;
  margin: 5px;
  font-size: 28px;
  text-align: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, .05);
}

.widget-heading {
  margin: 10px 0;
}

.themed-background-info {
  background-color: #5bc0de;
}

/* 表格定价样式 */
.table-pricing {
  background-color: #ffffff;
  text-align: center;
  border: 2px solid #ffffff;
  transition: all .15s ease-out;
}

.table-pricing:hover {
  border-color: #5ccdde;
  box-shadow: 0 0 20px rgba(0, 0, 0, .2);
}

/* 按钮效果 */
.btn-effect-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple 0.6s linear;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 媒体查询 */
@media (max-width: 767px) {
  .col-xs-12 {
    width: 100%;
  }

  .btn-group-justified > .btn,
  .btn-group-justified > .btn-group {
    display: block;
    width: 100%;
    margin-bottom: 5px;
  }

  .modal-dialog {
    margin: 10px;
  }
}

@media (min-width: 768px) {
  .col-sm-6 {
    width: 50%;
    float: left;
  }

  .col-sm-10 {
    width: 83.33333333%;
  }
}

@media (min-width: 992px) {
  .col-md-8 {
    width: 66.66666667%;
  }
}

@media (min-width: 1200px) {
  .col-lg-5 {
    width: 41.66666667%;
  }
}
</style>