<template>
  <div class="caihong-mall">
    <!-- 顶部标题区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="avatar-container">
          <div class="avatar">
            <div class="penguin-icon"></div>
          </div>
        </div>
        <h1 class="title">彩虹云商城</h1>
        <div class="nav-buttons">
          <button class="nav-btn">📢 公告</button>
          <button class="nav-btn">👤 客服</button>
          <button class="nav-btn">🔑 登录</button>
        </div>
      </div>
    </div>

    <!-- 导航栏 -->
    <div class="navigation-bar">
      <button class="nav-item">▼ 下单</button>
      <button class="nav-item">🔍 查询</button>
      <button class="nav-item active">◆ 分站</button>
      <button class="nav-item">≡ 更多</button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="promotion-info">
        <span class="heart-icon">♥</span>
        <span class="promotion-text">【07月04日】最新业务通知</span>
        <span class="promotion-badge">点击查看</span>
      </div>

      <div class="search-area">
        <div class="search-row">
          <input type="text" class="search-input" placeholder="搜索商品">
          <input type="text" class="search-input" placeholder="按需商品">
          <button class="search-btn">🔍</button>
        </div>

        <div class="category-row">
          <select class="category-select">
            <option>选择商品</option>
            <option>清洗拌商品</option>
          </select>
        </div>

        <div class="action-buttons">
          <button class="add-cart-btn">加入购物车</button>
          <button class="buy-now-btn">立即购买</button>
        </div>
      </div>
    </div>

    <!-- 数据统计区域 -->
    <div class="stats-section">
      <div class="stats-header">
        <span class="stats-icon">📊</span>
        <span class="stats-title">数据统计</span>
      </div>

      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">0元</div>
          <div class="stat-icon">🛡</div>
          <div class="stat-label">安全运营</div>
        </div>

        <div class="stat-item">
          <div class="stat-number">0元</div>
          <div class="stat-icon">�</div>
          <div class="stat-label">交易总数</div>
        </div>

        <div class="stat-item">
          <div class="stat-number">0笔</div>
          <div class="stat-icon">✓</div>
          <div class="stat-label">订单总数</div>
        </div>

        <div class="stat-item">
          <div class="stat-number">0个</div>
          <div class="stat-icon">👥</div>
          <div class="stat-label">代理总数</div>
        </div>

        <div class="stat-item">
          <div class="stat-number">0元</div>
          <div class="stat-icon">📈</div>
          <div class="stat-label">今日交易</div>
        </div>

        <div class="stat-item">
          <div class="stat-number">0笔</div>
          <div class="stat-icon">✓</div>
          <div class="stat-label">今日订单</div>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer">
      <div class="copyright">
        彩虹云商城 ❤️ 2025 | **************
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CaihongMall',
  data() {
    return {
      // 组件数据
    }
  },
  methods: {
    // 组件方法
  }
}
</script>

<style scoped>
.caihong-mall {
  max-width: 400px;
  margin: 0 auto;
  background-color: #f5f5f5;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* 顶部标题区域 */
.header-section {
  background-color: white;
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
}

.header-content {
  position: relative;
}

.avatar-container {
  margin-bottom: 15px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #4a90e2;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.penguin-icon {
  font-size: 40px;
  color: white;
}

.title {
  font-size: 24px;
  color: #4a90e2;
  margin: 15px 0;
  font-weight: bold;
}

.nav-buttons {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.nav-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-btn:hover {
  background-color: #f0f0f0;
}

/* 导航栏 */
.navigation-bar {
  background-color: white;
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.nav-item {
  flex: 1;
  background: none;
  border: none;
  padding: 15px 10px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
}

.nav-item.active {
  color: #ff6b35;
  border-bottom-color: #ff6b35;
}

.nav-item:hover {
  background-color: #f8f8f8;
}

/* 搜索区域 */
.search-section {
  background-color: white;
  padding: 15px;
  margin-bottom: 10px;
}

.promotion-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px;
  background-color: #fff5f5;
  border-radius: 4px;
}

.heart-icon {
  color: #ff6b35;
  margin-right: 8px;
}

.promotion-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.promotion-badge {
  background-color: #ff6b35;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.search-area {
  margin-top: 15px;
}

.search-row {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
}

.search-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-btn {
  padding: 10px 15px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.category-row {
  margin-bottom: 15px;
}

.category-select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.add-cart-btn {
  flex: 1;
  padding: 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-cart-btn:hover {
  background-color: #45a049;
}

.buy-now-btn {
  flex: 1;
  padding: 12px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.buy-now-btn:hover {
  background-color: #1976D2;
}

/* 数据统计区域 */
.stats-section {
  background-color: white;
  padding: 15px;
  margin-bottom: 10px;
}

.stats-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.stats-icon {
  margin-right: 8px;
  color: #4a90e2;
}

.stats-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 8px;
}

.stat-item .stat-icon {
  font-size: 24px;
  margin: 8px 0;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

/* 底部版权信息 */
.footer {
  background-color: white;
  padding: 15px;
  text-align: center;
  border-top: 1px solid #e0e0e0;
}

.copyright {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .caihong-mall {
    max-width: 100%;
    margin: 0;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .nav-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .search-row {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* 去除表情符号，使用纯文本图标 */
.penguin-icon {
  background-color: #333;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.penguin-icon::before {
  content: "🐧";
}

/* 优化按钮样式 */
.nav-btn, .nav-item, .search-btn, .add-cart-btn, .buy-now-btn {
  font-family: inherit;
  outline: none;
}

.nav-btn:focus, .nav-item:focus, .search-btn:focus,
.add-cart-btn:focus, .buy-now-btn:focus {
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
}
</style>